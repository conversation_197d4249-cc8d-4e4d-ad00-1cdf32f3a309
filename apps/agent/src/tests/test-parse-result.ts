import result from './sample.result.json';
import { extractJsonAdvanced } from '@/lib/utils';
import fs from 'fs';
import path from 'path';

type ScoutResult = {
  url: string;
  tier: string;
  reason: string;
  match_score: number;
  content_tags: string[];
  creatorMetrics: {
    region: string;
    language: string;
    nickname: string;
    unique_id: string;
    aweme_count: number;
    create_time: number;
    medianLikes: number;
    medianViews: number;
    averageLikes: number;
    averageViews: number;
    follower_count: number;
  };
};

type FlattenedScoutResult = {
  url: string;
  tier: string;
  reason: string;
  match_score: number;
  content_tags: string;
  creator_region: string;
  creator_language: string;
  creator_nickname: string;
  creator_unique_id: string;
  creator_aweme_count: number;
  creator_create_time: number;
  creator_medianLikes: number;
  creator_medianViews: number;
  creator_averageLikes: number;
  creator_averageViews: number;
  creator_follower_count: number;
};

function flattenScoutResult(result: ScoutResult): FlattenedScoutResult {
  return {
    url: result.url,
    tier: result.tier,
    reason: result.reason,
    match_score: result.match_score,
    content_tags: result.content_tags.join(';'), // Join array with semicolon
    creator_region: result.creatorMetrics.region,
    creator_language: result.creatorMetrics.language,
    creator_nickname: result.creatorMetrics.nickname,
    creator_unique_id: result.creatorMetrics.unique_id,
    creator_aweme_count: result.creatorMetrics.aweme_count,
    creator_create_time: result.creatorMetrics.create_time,
    creator_medianLikes: result.creatorMetrics.medianLikes,
    creator_medianViews: result.creatorMetrics.medianViews,
    creator_averageLikes: result.creatorMetrics.averageLikes,
    creator_averageViews: result.creatorMetrics.averageViews,
    creator_follower_count: result.creatorMetrics.follower_count,
  };
}

function escapeCSVField(field: any): string {
  const str = String(field);
  // If field contains comma, newline, or quote, wrap in quotes and escape internal quotes
  if (str.includes(',') || str.includes('\n') || str.includes('"')) {
    return `"${str.replace(/"/g, '""')}"`;
  }
  return str;
}

function arrayToCsvRow(row: any[]): string {
  return row.map(escapeCSVField).join(',');
}

export function parseScoutResult(input: string, outputCsv: string) {
  try {
    // Parse the input JSON string
    let scoutResults: ScoutResult[];

    if (input.trim().startsWith('[')) {
      // Input is already a JSON array
      scoutResults = JSON.parse(input);
    } else {
      // Try to extract JSON using the utility function
      const extractedJson = extractJsonAdvanced(input);
      scoutResults = Array.isArray(extractedJson)
        ? extractedJson
        : [extractedJson];
    }

    // Flatten the results
    const flattenedResults = scoutResults.map(flattenScoutResult);

    if (flattenedResults.length === 0) {
      console.warn('No results to export');
      return;
    }

    // Generate CSV content
    const headers = Object.keys(flattenedResults[0]);
    const csvContent = [
      arrayToCsvRow(headers), // Header row
      ...flattenedResults.map((result) =>
        arrayToCsvRow(headers.map((header) => (result as any)[header])),
      ),
    ].join('\n');

    // Ensure output directory exists
    const outputDir = path.dirname(outputCsv);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write CSV file
    fs.writeFileSync(outputCsv, csvContent, 'utf8');

    console.log(
      `Successfully exported ${flattenedResults.length} scout results to ${outputCsv}`,
    );

    return {
      success: true,
      recordsProcessed: flattenedResults.length,
      outputFile: outputCsv,
    };
  } catch (error) {
    console.error('Error parsing scout results:', error);
    throw new Error(
      `Failed to parse scout results: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

parseScoutResult(JSON.stringify(result.results), './output/scout_results.csv');

// Example usage:
// parseScoutResult(JSON.stringify(result), './output/scout_results.csv');
