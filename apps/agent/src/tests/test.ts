import { <PERSON><PERSON> } from "@mastra/core";
import { aiScriptWorkflow } from "@/workflows/aiScriptWorkflow";
import { campaignAnalyzer } from "@/agents/campaignAnalyzerAgent";

const mastra = new Mastra({
  agents: {
    campaignAnalyzer,
  },
  workflows: {
    aiScriptWorkflow,
  },
});

(async () => {
  // Workflow
  const { runId, start } = mastra.getWorkflow("aiScriptWorkflow").createRun();

  console.log("Run", runId);

  const runResult = await start({
    triggerData: { campaignDescription: "我想要对创建一个 MLBB 的推广活动" },
  });

  console.log("Final output:", runResult.results);

  // Agent
  // const agent = mastra.getAgent("campaignAnalyzer");
  // const resp = await agent.generate(
  //   [{ role: "user", content: "我想要对创建一个 MLBB 的推广活动" }],
  //   {
  //     toolChoice: "auto",
  //   }
  // );
  // console.log("resp", resp);
})();
