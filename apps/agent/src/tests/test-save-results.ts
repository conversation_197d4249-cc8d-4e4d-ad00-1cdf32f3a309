/**
 * Example usage of the updated saveCreatorResultsToFile function
 * This demonstrates how to handle the new workflow structure where
 * full results are stored in the database and accessed via contextId
 */

import { saveCreatorResultsToFile } from '../utils/saveResults';
import result from './sample.result.json';

// Example 2: Input parameters used for the workflow
const inputParameters = {
  targetCreatorDescription:
    'Fashion and lifestyle creators with high engagement',
  desiredCreatorCount: 50,
  filterMode: 'STRICT',
  useIntelligentChallengeSelection: true,
  minFollowers: 10000,
  minRecentMedianViews: 5000,
  minLikes: 1000,
  minComments: 50,
};

// Example usage
async function exampleUsage() {
  try {
    // Save results to files (both JSON and CSV)
    const savedPath = await saveCreatorResultsToFile(
      result,
      inputParameters,
      'scouting-results', // Optional: custom directory
      'fashion-creators', // Optional: custom filename prefix
    );

    console.log(`Results saved to: ${savedPath}`);

    // The function will:
    // 1. Check if contextId exists in the result
    // 2. Retrieve full creator data from database using the contextId
    // 3. Generate comprehensive JSON file with detailed, summary, and CSV-ready formats
    // 4. Generate CSV file with flattened metrics (arrays converted to comma-separated strings)
    // 5. Return the path to the saved JSON file
  } catch (error) {
    console.error('Error saving results:', error);
  }
}

exampleUsage();

// Notes on the new features:
// 1. The function now handles the new workflow structure where full data is in the database
// 2. CSV export is automatically generated with all metrics flattened
// 3. Arrays (like content_tags) are converted to comma-separated strings in CSV
// 4. The function is now async to support database retrieval
// 5. Both JSON and CSV files are saved with the same timestamp for easy correlation
