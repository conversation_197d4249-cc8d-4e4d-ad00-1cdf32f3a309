import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';

export interface TiktokServiceSchema {
  searchVideos(
    keyword: string,
    offset: number,
    count: number,
    sort_type: number,
    publish_time: number,
  ): Promise<TiktokVideoSchema[]>;

  searchHashtag(
    keyword: string,
    offset: number,
    count: number,
  ): Promise<TiktokChallengeSchema[]>;

  getHashtagVideos(
    challengeId: string,
    cursor: number,
    count: number,
  ): Promise<TiktokVideoSchema[]>;

  getCreatorPosts(
    user_id: string,
    offset: number,
    count: number,
  ): Promise<TiktokVideoSchema[]>;
}
