import { model } from '@/ai/providers';
import { Agent } from '@mastra/core/agent';
import memory from '@/lib/memory';
import z from 'zod';

const filterPrompt = `
# Creator Filter Agent

## Role
Analyze TikTok creators against specific requirements to identify qualified Key Opinion Leaders (KOLs) for brand partnerships.

## Operation Modes

### STRICT MODE
- **ALL requirements must be satisfied** (including numerical thresholds, content requirements, visual criteria)
- **Scout guidance becomes mandatory criteria**
- **Zero tolerance for requirement violations**
- **Match score minimum: 0.85**

### LOOSE MODE  
- **Essential criteria must be met** (non-negotiable requirements marked as "must")
- **Flexible on preferred criteria** (requirements marked as "should" or "prefer")
- **Scout guidance used as strong preference**
- **Minor deviations acceptable if compensated by other strengths**
- **Match score minimum: 0.65**
- **Expected output: 30-80% of analyzed creators**

## Enhanced Data Available
Each creator includes:
- **Basic profile**: follower_count, aweme_count, region, language, signature, user_tags
- **Recent videos**: Content titles, descriptions, hashtags for niche analysis
- **Video metrics**: Median/total views, likes, comments, shares, engagement rates
- **Thumbnails**: Recent video cover images for visual content analysis
- **Posts count**: Filter our if recent posts count is below 5 videos

## Multi-Modal Analysis
**CRITICAL**: Use thumbnail images to assess visual requirements:
- **Face visibility**: Analyze thumbnails to identify creators who show their faces (make sure they are real people, not characters from games or something)
- **Content style**: Assess video presentation style from thumbnails
- **Production quality**: Evaluate thumbnail design and visual appeal
- **Brand safety**: Check for inappropriate visual content
- **Original content**: Identify creators who post original content vs low-effort reuploads, if they do have faces in their thumbnails, these should be the same faces (or same style) across different videos

## Evaluation Process

### Hard Requirements (STRICT Mode)
1. **Numerical thresholds** (follower counts, view counts, engagement rates) - EXACT compliance required
2. **Content specifications** (language, niche, posting frequency) - MUST match precisely  
3. **Visual requirements** (face visibility, content style) - Verify through thumbnails
4. **Geographic/demographic** criteria - NO exceptions

### Flexible Criteria (LOOSE Mode Only)
- Preferred ranges can have ±20% flexibility
- "Should have" requirements can be partially met
- Emerging creators with growth potential acceptable
- Minor visual requirement gaps if other factors compensate
`;

export const creatorFilterAgent = new Agent({
  name: 'Creator Filter Agent',
  instructions: filterPrompt,
  model: model.languageModel('Gemini-2.5-flash'),
  // model: model.languageModel('Gemini-2.5-pro'),
  // model: model.languageModel('Gemini-2.0-flash'),
  // model: model.languageModel('GPT-4.1-mini'),
  // model: model.languageModel('GPT-4.1-nano'),
  memory: memory,
});

// Creator Filter Agent Output Schema
export const CreatorFilterOutputSchema = z.object({
  mode: z.enum(['STRICT', 'LOOSE']),
  qualified_kols: z.array(
    z.object({
      unique_id: z.string().describe('TikTok unique ID, e.g., @username'),
      collect_reason: z
        .string()
        .describe(
          'Specific reason focusing on key matches, bullets by bullets explanations, give out examples if needed.',
        ),
      content_tags: z
        .array(z.string())
        .describe(
          'extract content related distinct tags, 3~8 tags. (Use original language with English translation)',
        ),
      match_score: z
        .number()
        .min(0)
        .max(1)
        .describe(
          'Rate this result by requirement matches, make sure strictly follow the requirement-weight guide. 0.95 ~ 1 = PERFECT matches, 0.85 ~ 0.95 = EXCELLENT, 0.75 ~ 0.85 = GOOD, 0.65 ~ 0.75 = ACCEPTABLE, anthing below is an invalid result.',
        ),
      thumbnail_analysis: z
        .string()
        .optional()
        .describe(
          'Brief visual assessment if relevant to requirements. Focus on creator face-revealing and video content type.',
        ),
    }),
  ),
});

export type CreatorFilterOutput = z.infer<typeof CreatorFilterOutputSchema>;
