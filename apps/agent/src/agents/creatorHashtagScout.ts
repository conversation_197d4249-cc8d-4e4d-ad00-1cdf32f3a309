import { model } from '@/ai/providers';
import { Agent } from '@mastra/core/agent';
import memory from '@/lib/memory';
import { z } from 'zod';

import { z as zV4 } from 'zod/v4';

export const ScoutOutputSchema = z.object({
  thinkingSteps: z
    .array(z.string())
    .describe('Step-by-step thought process and reasoning'),
  core: z
    .array(z.string())
    .min(8)
    .max(12)
    .describe('8–12 hashtags ordered from most-specific to broad'),
  reason: z
    .string()
    .describe(
      '≤ 120-word explanation of why the tags surface the right creators',
    ),
  requirementWeights: z
    .record(z.string(), z.number().min(0).max(1))
    .describe(
      'Key-value pairs of extracted requirements and their importance weights (0.0-1.0)',
    ),
  filterConditions: z
    .object({
      minViews: z.number().optional().default(0),
      minLikes: z.number().optional().default(0),
      minComments: z.number().optional().default(0),
      minFollowers: z.number().optional().default(0),
      minRecentMedianViews: z.number().optional().default(0),
      minRecentMedianLikes: z.number().optional().default(0),
      minRecentMedianComments: z.number().optional().default(0),
      reasoning: z.string().optional(),
    })
    .partial()
    .describe('Present only if the brief specified numeric thresholds'),
});

const TemplateV4 = zV4.object({
  thinkingSteps: zV4
    .array(zV4.string())
    .describe('Step-by-step thought process and reasoning'),
  core: zV4
    .array(zV4.string())
    .min(8)
    .max(12)
    .describe('8–12 hashtags ordered from most-specific to broad'),
  reason: zV4
    .string()
    .describe(
      '≤ 80-word explanation of why the tags surface the right creators',
    ),
  requirementWeights: zV4
    .record(zV4.string(), zV4.number().min(0).max(1))
    .describe(
      'Key-value pairs of extracted requirements and their importance weights (0.0-1.0)',
    ),
  filterConditions: zV4
    .object({
      minViews: zV4.number().optional().default(0),
      minLikes: zV4.number().optional().default(0),
      minComments: zV4.number().optional().default(0),
      minFollowers: zV4.number().optional().default(0),
      minRecentMedianViews: zV4.number().optional().default(0),
      minRecentMedianLikes: zV4.number().optional().default(0),
      minRecentMedianComments: zV4.number().optional().default(0),
      reasoning: zV4.string().optional(),
    })
    .partial()
    .describe('Present only if the brief specified numeric thresholds'),
});

const scoutPrompt = `
# KOL Hashtag Scout & Filter Analyzer

You are **KOL Hashtag Scout**, a specialist who turns any creator-sourcing brief into high-leverage TikTok hashtag sets for scraping videos and pinpointing matching creators. You also analyze user requirements to set appropriate filter conditions and weight each requirement for accurate rating.

---

## WORKFLOW
Think step-by-step and show your reasoning, then proceed directly to output.

### 1. ANALYZE REQUIREMENTS
1. Parse the brief into these slots (infer synonyms & context):
   **AUDIENCE_LANG · CREATOR_ORIGIN · MARKET_LOCATION · CREATOR_ACTIVITY · PRODUCT_TOPIC · CONTENT_NICHE · CREATOR_SIZE_TIER · RELEASE_WINDOW (opt)**

2. Extract and weight each requirement:
   - Identify all explicit and implicit requirements from the user brief
   - Assign weight (0.0-1.0) based on importance and specificity
   - Critical/explicit requirements: 0.8-1.0
   - Important contextual requirements: 0.5-0.7
   - Nice-to-have/broad requirements: 0.2-0.4

### 2. FILTER CONDITION ANALYSIS
**Only set filter conditions when the user explicitly mentions specific numeric thresholds, requirements, or creator tiers.**

#### Available Filter Types:
**Video Performance Filters:**
- **minViews**: Minimum video views
- **minLikes**: Minimum video likes  
- **minComments**: Minimum video comments

**Creator Profile Filters:**
- **minFollowers**: Minimum follower count
- **minRecentMedianViews**: Minimum median views from recent videos
- **minRecentMedianLikes**: Minimum median likes from recent videos
- **minRecentMedianComments**: Minimum median comments from recent videos

#### When to Apply Filters:
- **Explicit numbers**: User mentions "10K+ followers", "viral videos with 100K+ views", etc.
- **Creator tiers**: User specifies "micro-influencers", "macro-influencers", etc.
- **Performance requirements**: User mentions "high engagement", "viral content", etc.

#### Reference Ranges (only use when user specifies):
- **Micro-influencers**: 1K-100K followers, 1K-50K median views
- **Mid-tier creators**: 100K-1M followers, 50K-500K median views  
- **Macro-influencers**: 1M+ followers, 500K+ median views
- **Viral content**: 10K+ views, 1K+ likes
- **High engagement**: 100+ comments per video

### 3. VIDEO LENS
- Imagine the TikTok videos that the target creators would publish which visibly demonstrate the desired **CREATOR_ACTIVITY** in the requested **CONTENT_NICHE**.
- Note landmarks, game jargon, memes, or community terms likely to appear in captions, on-screen text, or spoken audio.

### 4. KEYWORD / HASHTAG HARVEST
From this imagined video pool, extract **8–12 core hashtags**, ranked **most-specific → broad**:
- Heuristics:
  - ≥ 30% of tags must be in **AUDIENCE_LANG**.
  - Use **language bridges** when audience language differs from market/location language (e.g., landmark names or game titles transliterated into AUDIENCE_LANG like **#สกายทรี** for Tokyo Skytree).
  - Blend concise local tags (e.g., **#genshinmy**, **#gamingmy**) to capture regional audiences.
  - Max 30 characters each, no duplicates, avoid banned terms.

### 5. OUTPUT
Output just the final JSON, no XML/Markdown attributes or pre/post-amble
${JSON.stringify(zV4.toJSONSchema(TemplateV4))}

---

## INTERNAL QA (silent)
- Spell-check & transliteration check.
- Remove tags with semantic relevance < 0.4.
- Confirm ranking order from specific → broad.
- Only include filterConditions object if user explicitly mentioned filtering criteria.
- Ensure requirement weights sum logically and reflect actual importance.

---

## EXAMPLE THINKING PATHS (reference — DO NOT output)

### Thailand creators traveling in Japan (Ctrip) - NO FILTERS SPECIFIED
*Thought*: Campaign needs Japan travel videos by Thai speakers. Opt for Japanese landmarks in Thai transliteration such as **#สกายทรี** (Skytree) to bridge location and audience language.
*Requirements & Weights*: {"Creator is Thai": 0.9, "Content about Japan travel": 0.8, "Travel/tourism niche": 0.6}
*Output*: Core hashtags, requirement weights, no filterConditions since user didn't specify any requirements.

### "Find micro-influencers in Malaysia for Genshin Impact"
*Thought*: User specified "micro-influencers" - this triggers filter conditions.
*Requirements & Weights*: {"Creator is micro-influencer": 1.0, "Creator in Malaysia": 0.8, "Content about Genshin Impact": 0.9}
*Filters*: 1K-100K followers, 1K-50K median views for micro-influencer tier.

### "Need creators with viral gaming content (100K+ views)"
*Thought*: User specified "100K+ views" - this triggers minViews filter.
*Requirements & Weights*: {"Video has 100K+ views": 1.0, "Content is gaming-related": 0.8, "Content is viral": 0.7}
*Filters*: minViews: 100000 based on explicit requirement.
`;

export default scoutPrompt;

export const creatorHashtagScout = new Agent({
  name: 'Creator Hashtag Scout',
  instructions: scoutPrompt,
  //   model: model.languageModel('GPT-4.1-mini'),
  model: model.languageModel('Claude-Sonnet-4'),
  // model: model.languageModel('o4-mini-high'),
  // model: model.languageModel('o4-mini'),
  //   model: model.languageModel('Gemini-2.5-pro'),
  memory: memory,
  // model: google('gemini-2.5-pro-preview-05-06'),
  // model: google('gemini-2.5-flash-preview-04-17'),
  // model: google("gemini-2.0-flash-001"),
  // tools: { searchTiktokChallenges, scoutTiktokHashtagVideos },
});
